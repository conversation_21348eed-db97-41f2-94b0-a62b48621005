{"name": "@zpi/blueprint-app", "version": "1.0.1", "author": "ZPI team", "description": "Micro app blueprint", "proxy": "https://socialdev.zalopay.vn", "repository": {"type": "git", "url": "https://gitlab.zalopay.vn/feplatform/spa-framework/blueprint-app.git"}, "files": ["dist", "src"], "keywords": ["spa", "blueprint-app"], "scripts": {"ma-start": "micro-app-scripts start", "test": "micro-app-scripts test", "version-dev": "npm version prerelease --preid=alpha --no-git-tag-version --no-commit-hooks", "version-stg": "npm version prerelease --preid=beta --no-git-tag-version --no-commit-hooks", "wp-build": "micro-app-scripts build", "publish": "npm publish", "lint": "eslint -c .eslintrc.js --ext .ts,.tsx .", "lint:fix": "npm run lint -- --fix", "prepare": "husky install"}, "publishConfig": {"registry": "https://repo.zalopay.vn/verdaccio/"}, "browserslist": ["Android >= 5", "IOS >= 9.3", "Edge >= 15", "Safari >= 9.1", "Chrome >= 49", "Firefox >= 31", "Samsung >= 5"], "dependencies": {"@zpi/federation-adapter": "0.1.1", "@zpi/single-spa-react": "2.1.1", "react": "18.3.1", "react-dom": "18.3.1", "react-router-dom": "5.3.3"}, "devDependencies": {"@types/react": "18.3.9", "@types/react-dom": "18.3.0", "@types/react-router-dom": "5.3.3", "@zpi/micro-app-cli": "1.0.6", "eslint-config-prettier": "7.1.0", "eslint-plugin-prettier": "3.3.1", "husky": "7.0.0", "prettier": "2.2.1", "single-spa": "5.9.3", "typescript": "4.2.2"}}