stages:
  - valid
  - install_dependencies
  - build
  - sync_resource
  - create_release

variables:
  STAGING_BRANCH: "staging"
  DEVELOP_BRANCH: "develop"

check-version-exist:
  stage: valid
  script:
    # Execute check existing of version script
    - sh scripts/check-version-exist.sh
  rules:
    # Execute jobs when a commit to master|staging|develop branch
    - if: '$CI_PIPELINE_SOURCE == "push" && (
        $CI_COMMIT_BRANCH == $DEVELOP_BRANCH ||
        $CI_COMMIT_BRANCH == $STAGING_BRANCH )'
  tags:
    - shell-executor-7118

install_dependencies:
  stage: install_dependencies
  script:
    # Auth private NPM
    - |
      {
        echo "registry=https://repo.zalopay.vn/verdaccio/"
        echo "//repo.zalopay.vn/verdaccio/:_authToken=\${VERDACCIO_ZTOOL_TOKEN}"
      } | tee -a .npmrc
    # Install dependencies
    - npm ci --cache .npm --prefer-offline
  rules:
    # Execute jobs when a commit to master|staging|develop branch
    - if: '$CI_PIPELINE_SOURCE == "push" && (
        $CI_COMMIT_BRANCH == $DEVELOP_BRANCH ||
        $CI_COMMIT_BRANCH == $STAGING_BRANCH )'
  cache:
    key: ${CI_COMMIT_REF_SLUG}
    paths:
      - .npm/
      - node_modules/
    policy: pull-push
  tags:
    - shell-executor-7118

build:
  stage: build
  script:
    # Execute build resource script
    - sh scripts/build-resource.sh
  cache:
    key: ${CI_COMMIT_REF_SLUG}
    paths:
      - node_modules/
    policy: pull
  artifacts:
    paths:
      - webpack-build/
  rules:
    # Execute jobs when a commit to master|staging|develop branch
    - if: '$CI_PIPELINE_SOURCE == "push" && (
        $CI_COMMIT_BRANCH == $DEVELOP_BRANCH ||
        $CI_COMMIT_BRANCH == $STAGING_BRANCH )'
  tags:
    - shell-executor-7118

sync_resource:
  stage: sync_resource
  script:
    # Execute sync resource script
    - sh scripts/sync-resource.sh
  artifacts:
    paths:
      - webpack-build/
  rules:
    # Execute jobs when a commit to master|staging|develop branch
    - if: '$CI_PIPELINE_SOURCE == "push" && (
        $CI_COMMIT_BRANCH == $DEVELOP_BRANCH ||
        $CI_COMMIT_BRANCH == $STAGING_BRANCH )'
  tags:
    - shell-executor-7118

create_release:
  stage: create_release
  script:
    # Execute create release script
    - sh scripts/create-release.sh
  rules:
    # Execute jobs when a commit to master|staging|develop branch
    - if: '$CI_PIPELINE_SOURCE == "push" && (
        $CI_COMMIT_BRANCH == $DEVELOP_BRANCH ||
        $CI_COMMIT_BRANCH == $STAGING_BRANCH )'
  tags:
    - shell-executor-7118
