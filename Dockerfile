FROM registry-gitlab.zalopay.vn/docker/images/node:14-alpine AS base

# Arguments passed from docker build command
ARG VERDACCIO_ZTOOL_TOKEN
ARG VAR_ENV

FROM base AS prune

# Check https://github.com/nodejs/docker-node/tree/b4117f9333da4138b03a546ec926ef50a31506c3#nodealpine to understand why libc6-compat might be needed.
RUN apk add --no-cache libc6-compat

# Set working directory
WORKDIR /app

# Copy files that are needed for installing dependencies
COPY .gitignore .gitignore
COPY .npmrc .npmrc
COPY package.json ./package.json
COPY package-lock.json ./package-lock.json

# Security read more https://www.alexandraulsh.com/2018/06/25/docker-npmrc-security/
ENV NPM_TOKEN=$VERDACCIO_ZTOOL_TOKEN
ENV VAR_ENV=$VAR_ENV
ENV CI_PIPELINE_SOURCE=jen<PERSON>

# Clone the code from .gitlab-ci.yml
RUN echo "//repo.zalopay.vn/verdaccio/:_authToken=${NPM_TOKEN}" >> .npmrc && \
    echo "//repo.zalopay.vn:443/verdaccio/:_authToken=${NPM_TOKEN}" >> .npmrc

RUN npm ci

COPY . .

# Sync static
RUN echo "Target env: ${VAR_ENV}"
RUN sh scripts/jenkins/deployment.sh
