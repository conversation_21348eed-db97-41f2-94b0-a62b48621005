#!/usr/bin/env bash

HOME=$(pwd)

# Extract version value from package.json
npm_package_version=$(node -p "require('./package.json').version")

# Load .env file
if [ -e "${HOME}/.env" ]; then
  . "${HOME}/.env"
fi

if [ $1 == "pro" ]; then
  # Load .env.product file
  . "${HOME}/.env.production"
fi

# ToDo switch environment when build time
# if [ ! -z ${VAR_ENV} ]; then
#   if [ ${VAR_ENV} == "qc" ]; then
#     # Load .env.develop file
#     . "${HOME}/.env.develop"
#   elif [ ${VAR_ENV} == "stg" ]; then
#     # Load .env.staging file
#     . "${HOME}/.env.staging"
#   elif [ ${VAR_ENV} == "pro" ]; then
#     # Load .env.product file
#     . "${HOME}/.env.production"
#   fi
# fi


# export CDN variables
export APP_ID=$APP_ID
export APP_NAME=$APP_NAME
export BUILD_DIR=$BUILD_DIR
export CONFIG_TOOL_API_URL=$CONFIG_TOOL_API_URL
export CDN_USERNAME=$CDN_USERNAME
export CDN_SERVER_IP=$CDN_SERVER_IP
export CDN_SERVER_DIR=$CDN_SERVER_DIR

# export variables environent
export npm_package_version=$npm_package_version
export PUBLIC_URL=$PUBLIC_URL
export REACT_APP_ENV=$REACT_APP_ENV
export REACT_APP_COMMON_BASE_URL=$REACT_APP_COMMON_BASE_URL
export GENERATE_SOURCEMAP=$GENERATE_SOURCEMAP
