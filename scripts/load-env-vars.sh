#!/usr/bin/env bash
 
HOME=$(pwd)
 
# Extract version value from package.json
npm_package_version=$(node -p "require('./package.json').version")
 
# Load .env file
if [ -e "${HOME}/.env" ]; then
  . "${HOME}/.env"
fi
 
if [ ! -z $CI_COMMIT_BRANCH ]; then
  if [ $CI_COMMIT_BRANCH == $DEVELOP_BRANCH ] || [ $CI_COMMIT_BRANCH == "develop" ]; then
    # Load .env.develop file
    . "${HOME}/.env.develop"
  elif [ $CI_COMMIT_BRANCH == $STAGING_BRANCH ] || [ $CI_COMMIT_BRANCH == "staging" ]; then
    # Load .env.staging file
    . "${HOME}/.env.staging"
  elif [ $CI_COMMIT_BRANCH == $PRODUCTION_BRANCH ] || [ $CI_COMMIT_BRANCH == "master" ]; then
    # Load .env.production file
    . "${HOME}/.env.production"
  fi
fi
 
export npm_package_version=$npm_package_version
export PUBLIC_URL=$PUBLIC_URL
export REACT_APP_ENV=$REACT_APP_ENV
export REACT_APP_COMMON_BASE_URL=$REACT_APP_COMMON_BASE_URL
export GENERATE_SOURCEMAP=$GENERATE_SOURCEMAP