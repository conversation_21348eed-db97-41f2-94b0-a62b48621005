const { join } = require('path');
const deps = require('./package.json').dependencies;

// Config remotes
const isRunLocal = process.env.REACT_APP_ENV === 'local';
console.log('=== REACT_APP_ENV=%s, isRunLocal=%s', process.env.REACT_APP_ENV, isRunLocal);

let appName = 'blueprint_app';
let exposesApp = { './BlueprintApp': join(__dirname, './src/blueprint-app') };

if (isRunLocal) {
  appName = 'test_app';
  exposesApp = { './TestApp': join(__dirname, './src/blueprint-app') };
}

const remotesLocal = {
  cashier_app: "cashier_app@https://staticsbqc.zalopay.com.vn/static/registry_federation/spa/v2/micro-apps/cashier-app/0.0.37-alpha.3/remoteEntry.js",
}
const remotes = isRunLocal ? remotesLocal : {};

module.exports = (env) => {
  return {
    name: appName,
    filename: 'remoteEntry.js',
    exposes: exposesApp,
    remotes,
    shared: [
      {
        "react-dom": {
          shareKey: "react-dom@18",
          singleton: true,
          requiredVersion: "18.3.1",
        },
        react: {
          shareKey: "react@18",
          singleton: true,
          requiredVersion: "18.3.1",
        },
        "react-router-dom": {
          singleton: true,
          requiredVersion: deps["react-router-dom"],
          shareKey: "react-router-dom@5",
        },
      },
    ],
  };
};