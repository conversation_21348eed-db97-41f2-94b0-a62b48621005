# Blueprint app

This is boilerplate for creating a new micro-app. Project tech stack includes:

- React 18
- React-router-dom 5.3.3<br/>
  And some internal packages:
- @zpi/federation-adapter: for using module federation components [Sharing module federation components: Best practices](https://confluence.zalopay.vn/display/ZTM/%5BPCT-WS%5D+3.+Building+compatible+wrapper+for+shared+modules)

# The DAP model

![Note-70](/uploads/35226baa8a1c9eb439ba5b911a0a3d3d/Note-70.jpg)

The DAP model includes 3 layers: Data source, Application, Presentation. We can use this model for many usecases: apply at container level, page level, a whole app level, or a module level that is splitted by business domain.
For example: a micro app in ZPI should apply this model at page level due to already splitted by business domain.

A module that apply the DAP model is called a DAP module. Each DAP module should isolate each other. All the sharable code among DAP modules should place in one place. The shareable code can be constants, utilities,..

Each layer can include utilities, constants, type definition

### Data source

- Includes the code for fetching data from API, Web storage,...
- This layer must not depend on Application, Presentation

### Application

- Includes all logic that is necessary for Presentation such as: data fetching, event handlers, component state, complex UI handling like drag and drop,...
- Communicate with Data source to get data, map it to data format of Presentation.
- All logic should be implemented by hooks to make it more sharable
- This layer must only depend on Data source.

### Presentation

- Includes the code for display UI such as: CSS style, React component,...
- Communicate with Application layer via hooks to get data, event handlers,...
- This layer must only depend on Application

# The blueprint app

This blueprint app provide a basic React boilerplate that includes:

- A DAP project structure with zpi-scripts that extend from react-scripts. So we can use almost features from react-scripts and module federation from webpack 5.
- A pre-configured Airbnb eslint and prettier.
- A husky setup to check style guilde before committing.
- A build tool setup to bundle code as a micro app or a standalone app.

# Usage

## Available scripts

- `npm start`: run as standalone app at local.
- `npm run build`: build as a standalone app
- `npm run build-pkg`: package the code before publishing to Verdaccio registry.
- `npm run publish`: publish the package.
- `npm run lint`: check eslint
- `npm run lint:fix`: fix the eslint issues if possible
- `npm run test`: run the test. This feature is the same with react-scripts

## Styles

- Edit the `prefixCls` in zpi.config.js to your micro-app name when using CSS module. It's recommended to use CSS module or other stuffs that isolate the micro-app style to avoid CSS collision

## Enviroment

- The same with create react app

## State management

- This blueprint does not include any state managment libraries. So add new one if you need.

# Notes

- The package verison should follow semver versioning rule.
- There is a difference in a way the micro-app code is run in the local development and the dev/stg/prod environment. At the local development, we use webpack to run directly the micro-app code, but in the dev/stg/prod environment, the micro-app code is packaged as npm package by rollup before ship into the main-app to bundle. So, sometimes there are some code that works at the local devlopment but not at other envs. Be noticed to not be too surprised. This note just only valid for micro app that integrate with main app v1.
