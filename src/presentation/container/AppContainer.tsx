import React, { FunctionComponent } from 'react';
import { BrowserRouter as Router, Route, Switch } from 'react-router-dom';

import { PATH } from '@share/constant/path';
import loadable from '@share/Loadable';

const PageA = loadable(() => import(/* webpackChunkName: "PageA" */ '@presentation/page/PageA'));
const PageB = loadable(() => import(/* webpackChunkName: "PageB" */ '@presentation/page/PageB'));

const Routes = () => {
  return (
    <Switch>
      <Route path={PATH.PAGE_A} component={PageA} />
      <Route exact path={PATH.PAGE_B} component={PageB} />
    </Switch>
  );
};

const AppContainer: FunctionComponent<any> = () => {
  const baseName = window.__BASE_NAME__ || '/spa/v2';
  return (
    <Router basename={`${baseName}`}>
      <Routes />
    </Router>
  );
};

export default AppContainer;
