// src/blueprint-app.ts
import React from 'react';
import ReactD<PERSON><PERSON>lient from 'react-dom/client';

// the corresponding single-spa ecosystem.
import singleSpaReact from '@zpi/single-spa-react';
import { AppContainer } from '@presentation/container';

const reactLifecycles = singleSpaReact({
  React,
  ReactDOMClient,
  rootComponent: AppContainer,
});

export const bootstrap = reactLifecycles.bootstrap;

export const mount = (props: any) => {
  return reactLifecycles.mount({ ...props, name: 'root' });
};

export const unmount = (props: any) => {
  return reactLifecycles.unmount({ ...props, name: 'root' });
};
