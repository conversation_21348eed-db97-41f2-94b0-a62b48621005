import environments from '../constant/environment';

let __ENV__ = process.env.REACT_APP_ENV || environments.DEVELOP;

function getConfig(env: string) {
  // use for local develop
  switch (env) {
    case environments.LOCAL:
      return require('./development');
    case environments.DEVELOP:
      return require('./develop');
    case environments.STAGING:
      return require('./staging');
    case environments.PRODUCTION:
      return require('./production');
    default:
      return require('./develop');
  }
}

const appConfig: AppConfiguration = {
  ...getConfig(__ENV__),
  set environment(value) {
    __ENV__ = value;
    const config = getConfig(__ENV__);
    Object.keys(config).forEach((k) => {
      // @ts-ignore
      this[k] = config[k];
    });
  },
  get environment() {
    return __ENV__;
  },
};

export default appConfig;
